# Azure AI Foundry Agents Samples

Welcome to the **Azure AI Foundry Agents Samples** repository! This collection showcases sample implementations, templates, and demos for building intelligent agents using **Azure AI Foundry**. Whether you're just getting started or looking to deepen your skills, these examples will help you harness the power of AI in your applications.

## 🔍 What is Azure AI Foundry?

Azure AI Foundry is a trusted, integrated platform for Developers and IT Administrators to design, customize, and manage AI applications and agents.

For more details, please refer to [Azure AI Foundry documentation](https://learn.microsoft.com/en-us/azure/ai-foundry/what-is-azure-ai-foundry).

## 🛠️ Requirements

- An active Azure subscription
- Access to Azure AI Foundry
- Python 3.10+
- NPM and Node.js to run the MCP Server
- VS Code with Azure extensions recommended

## 🚀 Getting Started

1. Clone the repo:
```
git clone https://github.com/Azure-Samples/ai-foundry-agents-samples.git
```

2. Navigate to the folder of interest:
```
cd examples/<examples-name>
```

3. Follow the instructions in the `README.md` file within each sample directory to run the example.

## 📄 License

This project is licensed under the MIT License. See `LICENSE.txt` for more information.

## 🙌 Contributing

Have a cool agent or MCP integration to share?  
Pull requests are welcome! Please open an issue first to discuss what you would like to add.
