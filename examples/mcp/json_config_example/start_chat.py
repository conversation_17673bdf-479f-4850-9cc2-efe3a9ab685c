#!/usr/bin/env python3
"""
Quick launcher for the chat client with MCP configuration.

This script provides an easy way to start the pretty chat client with your
MCP configuration file. The pretty chat client is now the default.

Usage:
    python start_chat.py [config_file] [--client=CLIENT_TYPE]

Examples:
    python start_chat.py
    python start_chat.py /path/to/mcp.json
    python start_chat.py --client=basic
    python start_chat.py /path/to/mcp.json --client=enhanced
"""

import argparse
import sys
import os

# Your MCP configuration file path
DEFAULT_MCP_CONFIG = "/Users/<USER>/dev/personal/local_llm/docker_compose_ollama/mcp.json"

def main():
    """Main launcher function."""

    parser = argparse.ArgumentParser(
        description="Quick launcher for MCP Chat Client",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python start_chat.py
  python start_chat.py /path/to/mcp.json
  python start_chat.py --client=basic
  python start_chat.py /path/to/mcp.json --client=enhanced
        """
    )

    parser.add_argument(
        "config_file",
        nargs="?",
        default=DEFAULT_MCP_CONFIG,
        help=f"Path to MCP configuration JSON file (default: {DEFAULT_MCP_CONFIG})"
    )

    parser.add_argument(
        "--client",
        choices=["basic", "enhanced", "pretty"],
        default="pretty",
        help="Chat client type to use (default: pretty)"
    )

    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Show interactive client selection menu"
    )

    args = parser.parse_args()

    print("🚀 Starting Chat Client with MCP Server Support")
    print("=" * 50)

    # Check if config file exists
    config_path = args.config_file

    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        print()
        print("Options:")
        print("1. Use the default example config:")
        print("   python pretty_chat_client.py")
        print()
        print("2. Specify a different config file:")
        print("   python start_chat.py /path/to/your/mcp.json")
        print()
        return 1

    print(f"📁 Using config: {config_path}")
    print()

    # Determine which client to use
    if args.interactive:
        # Show interactive menu
        print("Choose your chat client:")
        print("1. Basic Chat Client")
        print("2. Enhanced Chat Client with history")
        print("3. Pretty Chat Client with beautiful formatting (default)")
        print()

        try:
            choice = input("Enter your choice (1-3, default: 3): ").strip()
            if not choice:
                choice = "3"

            if choice == "1":
                client_type = "basic"
            elif choice == "2":
                client_type = "enhanced"
            elif choice == "3":
                client_type = "pretty"
            else:
                print("❌ Invalid choice. Using pretty client.")
                client_type = "pretty"

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            return 0
    else:
        # Use the specified client type
        client_type = args.client

    # Map client type to script name
    client_scripts = {
        "basic": "chat_client.py",
        "enhanced": "enhanced_chat_client.py",
        "pretty": "pretty_chat_client.py"
    }

    script = client_scripts[client_type]

    print(f"🎯 Starting {client_type.title()} Chat Client ({script})...")
    print()

    try:
        # Run the selected chat client
        os.system(f"python {script} --config \"{config_path}\"")

    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        return 0
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
