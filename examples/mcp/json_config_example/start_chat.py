#!/usr/bin/env python3
"""
Quick launcher for the chat client with your MCP configuration.

This script provides an easy way to start the chat client with your
specific MCP configuration file.
"""

import asyncio
import sys
import os

# Your MCP configuration file path
DEFAULT_MCP_CONFIG = "/Users/<USER>/dev/personal/local_llm/docker_compose_ollama/mcp.json"

def main():
    """Main launcher function."""
    
    print("🚀 Starting Chat Client with MCP Server Support")
    print("=" * 50)
    
    # Check if config file exists
    config_path = DEFAULT_MCP_CONFIG
    if len(sys.argv) > 1:
        config_path = sys.argv[1]
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        print()
        print("Options:")
        print("1. Use the default example config:")
        print("   python chat_client.py")
        print()
        print("2. Specify a different config file:")
        print("   python start_chat.py /path/to/your/mcp.json")
        print()
        return 1
    
    print(f"📁 Using config: {config_path}")
    print()
    
    # Choose which client to run
    print("Choose your chat client:")
    print("1. Basic Chat Client (chat_client.py)")
    print("2. Enhanced Chat Client with history (enhanced_chat_client.py)")
    print()
    
    try:
        choice = input("Enter your choice (1 or 2, default: 2): ").strip()
        if not choice:
            choice = "2"
        
        if choice == "1":
            script = "chat_client.py"
        elif choice == "2":
            script = "enhanced_chat_client.py"
        else:
            print("❌ Invalid choice. Using enhanced client.")
            script = "enhanced_chat_client.py"
        
        print(f"🎯 Starting {script}...")
        print()
        
        # Run the selected chat client
        os.system(f"python {script} --config \"{config_path}\"")
        
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        return 0
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
