#!/usr/bin/env python3
"""
Simple wrapper to start the pretty chat client with your MCP configuration.

This is the simplest way to start chatting with your MCP servers.
Just run: python chat.py
"""

import sys
import os

# Your MCP configuration file path
MCP_CONFIG = "/Users/<USER>/dev/personal/local_llm/docker_compose_ollama/mcp.json"

def main():
    """Start the pretty chat client with your MCP configuration."""
    
    # Check if config file exists
    if not os.path.exists(MCP_CONFIG):
        print(f"❌ Configuration file not found: {MCP_CONFIG}")
        print()
        print("Please make sure your MCP configuration file exists at:")
        print(f"   {MCP_CONFIG}")
        print()
        print("Or use the start_chat.py script with a custom path:")
        print("   python start_chat.py /path/to/your/mcp.json")
        return 1
    
    print("🚀 Starting Pretty Chat Client with your MCP configuration...")
    print(f"📁 Config: {MCP_CONFIG}")
    print()
    
    try:
        # Run the pretty chat client with your config
        os.system(f"python pretty_chat_client.py --config \"{MCP_CONFIG}\"")
        
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        return 0
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
