# MCP JSON Configuration Example

This example demonstrates how to load and use multiple MCP servers from a JSON configuration file. It extends the basic filesystem example to support loading MCP server configurations from JSON, making it easier to manage multiple servers and their configurations.

## Features

- **JSON Configuration**: Load MCP server configurations from a JSON file
- **Multiple Servers**: Support for multiple MCP servers simultaneously
- **Flexible Configuration**: Easy to add, remove, or modify server configurations
- **Path Resolution**: Automatic resolution of relative paths
- **Command Line Support**: Specify custom configuration files via command line arguments

## Configuration Format

The JSON configuration follows this structure:

```json
{
  "mcpServers": {
    "ServerName": {
      "command": "command_to_run",
      "args": ["arg1", "arg2", "..."],
      "env": {
        "ENV_VAR": "value"
      }
    }
  }
}
```

### Example Configuration

The included `mcp_config.json` demonstrates two servers:

1. **FilesystemServer**: Provides file system access to the `sample_files` directory
2. **ControlPlaneServer**: A custom MCP server for control plane operations

## Usage

### Interactive Chat Client (Recommended)

The project includes interactive chat clients for a better user experience:

#### Quick Start with Your Config
```bash
python start_chat.py
```

This will automatically use your MCP configuration and let you choose between chat clients.

#### Basic Chat Client
```bash
python chat_client.py --config /path/to/your/mcp.json
```

#### Enhanced Chat Client (with history, save/load)
```bash
python enhanced_chat_client.py --config /path/to/your/mcp.json
```

### Non-Interactive Demo

#### Basic Usage
```bash
python main_azure_ai_foundry.py
```

This will use the default `mcp_config.json` configuration file.

#### Custom Configuration File
```bash
python main_azure_ai_foundry.py --config /path/to/your/mcp.json
```

You can specify a custom configuration file using the `--config` argument.

## Setup Instructions

### 1. Create and activate Python virtual environment

```bash
python3 -m venv .venv
```

#### MacOS/Linux
```bash
source .venv/bin/activate
```

#### Windows
```bash
venv\Scripts\activate
```

### 2. Install dependencies

```bash
pip install -r requirements.txt
```

### 3. Install npx (for filesystem server)

```bash
npm install -g npx
```

### 4. Configure environment variables

Rename `.env_sample` to `.env` and update the following variables:

- `AZURE_OPENAI_ENDPOINT`
- `AZURE_OPENAI_API_KEY`
- `AZURE_OPENAI_CHAT_DEPLOYMENT_NAME`
- `AZURE_OPENAI_API_VERSION`

### 5. Run the example

```bash
python main_azure_ai_foundry.py
```

## How It Works

1. **Configuration Loading**: The `MCPConfigLoader` class loads and parses the JSON configuration
2. **Server Creation**: For each enabled server in the configuration, an `MCPServerStdio` instance is created
3. **Path Resolution**: Relative paths in the configuration are resolved relative to the config file directory
4. **Agent Integration**: All configured servers are passed to the Agent for use

## Adding New Servers

To add a new MCP server, simply add it to the `mcpServers` object in your JSON configuration:

```json
{
  "mcpServers": {
    "YourNewServer": {
      "command": "your_command",
      "args": ["arg1", "arg2"],
      "env": {
        "REQUIRED_ENV_VAR": "value"
      }
    }
  }
}
```

## Using Your Own MCP Configuration

To use your own MCP configuration file (like the one at `/Users/<USER>/dev/personal/local_llm/docker_compose_ollama/mcp.json`):

```bash
python main_azure_ai_foundry.py --config /Users/<USER>/dev/personal/local_llm/docker_compose_ollama/mcp.json
```

The loader will automatically adapt to your configuration format and create the appropriate MCP server instances.

## Architecture

- **`mcp_loader.py`**: Core utility for loading MCP configurations from JSON
- **`main_azure_ai_foundry.py`**: Main application that uses the loader and runs the agent
- **`mcp_config.json`**: Example configuration file
- **`sample_files/`**: Sample data for the filesystem server

This modular approach makes it easy to reuse the MCP loading functionality in other projects.
