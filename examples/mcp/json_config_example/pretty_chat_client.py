#!/usr/bin/env python3
"""
Pretty Chat Client with Beautiful Formatting

This chat client provides beautiful formatting similar to Claude CLI and mcphost,
with syntax highlighting, structured output, and rich formatting.
"""

import asyncio
import os
import argparse
import sys
import json
from datetime import datetime
from typing import List, Dict, Any

from agents import Agent, OpenAIChatCompletionsModel, Runner, set_tracing_disabled
from agents.mcp import MCPServer
from dotenv import load_dotenv
from openai import AsyncAzureOpenAI

from mcp_loader import load_mcp_servers_from_config
from pretty_printer import Pretty<PERSON><PERSON><PERSON>
from tool_call_tracker import EnhancedRunner, MCPToolCallInterceptor


class PrettyChatClient:
    """Beautiful chat client with rich formatting and syntax highlighting."""

    def __init__(self, mcp_servers: List[MCPServer], use_rich: bool = True):
        """Initialize the pretty chat client with MCP servers."""
        self.mcp_servers = mcp_servers
        self.agent = None
        self.conversation_history = []
        self.session_start = datetime.now()
        self.printer = PrettyPrinter(use_rich=use_rich)
        self.enhanced_runner = EnhancedRunner(self.printer)
        self.tool_interceptor = MCPToolCallInterceptor(self.printer)

    def get_azure_open_ai_client(self):
        """Create and return Azure OpenAI client instance."""
        load_dotenv()

        return AsyncAzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
        )

    async def initialize_agent(self):
        """Initialize the AI agent with MCP servers."""
        azure_open_ai_client = self.get_azure_open_ai_client()
        set_tracing_disabled(disabled=True)

        # Patch MCP servers to intercept tool calls
        for server in self.mcp_servers:
            self.tool_interceptor.patch_mcp_server(server)

        # Create agent with all MCP servers
        self.agent = Agent(
            name="Assistant",
            instructions="""You are a helpful AI assistant with access to various tools through MCP servers.

You should:
- Be conversational, friendly, and helpful
- Use available tools when they can help answer questions or perform tasks
- Explain what you're doing when using tools
- Provide clear, well-formatted responses with proper markdown when appropriate
- Use code blocks with language specification for code examples
- Structure your responses with headers, lists, and formatting for better readability
- Ask clarifying questions when needed

Available capabilities depend on the configured MCP servers, which may include:
- File system operations (reading, writing, listing files)
- Control plane management and operations
- Database queries and management
- Web searches and data retrieval
- And more depending on your specific configuration

When showing code, data, or structured information:
- Use appropriate markdown formatting
- Specify language for code blocks (```python, ```json, ```bash, etc.)
- Use tables for tabular data when appropriate
- Use lists and headers to organize information clearly

Always be transparent about what tools you're using and what information you're finding.
            """,
            model=OpenAIChatCompletionsModel(
                model=os.getenv("AZURE_OPENAI_CHAT_DEPLOYMENT_NAME"),
                openai_client=azure_open_ai_client
            ),
            mcp_servers=self.mcp_servers,
        )

        server_names = [server.name for server in self.mcp_servers]
        self.printer.print_success(f"Agent initialized with {len(self.mcp_servers)} MCP server(s)")
        return server_names

    def add_to_history(self, user_input: str, assistant_response: str):
        """Add interaction to conversation history."""
        self.conversation_history.append({
            "timestamp": datetime.now().isoformat(),
            "user": user_input,
            "assistant": assistant_response
        })

    async def handle_special_commands(self, user_input: str) -> bool:
        """Handle special commands. Returns True if command was handled."""
        command = user_input.lower().strip()

        if command in ['quit', 'exit', 'bye']:
            self.printer.print_rule()
            self.printer.print_info(f"Session ended after {len(self.conversation_history)} interactions.")
            self.printer.print_success("Thanks for chatting! Goodbye! 👋")
            return True

        elif command == 'help':
            self._show_help()
            return True

        elif command == 'tools':
            self.printer.print_info("Requesting available tools from the assistant...")
            try:
                result = await self.enhanced_runner.run_with_tool_tracking(
                    self.agent,
                    "What tools do you have available? Please list them with brief descriptions of what each tool can do. Format your response with proper markdown."
                )
                response = result.final_output
                self.printer.print_assistant_response(response)
                self.add_to_history("tools", response)
            except Exception as e:
                error_msg = f"Error getting tools: {e}"
                self.printer.print_error(error_msg)
                self.add_to_history("tools", error_msg)
            return True

        elif command == 'history':
            self._show_history()
            return True

        elif command == 'save':
            self._save_conversation()
            return True

        elif command == 'clear':
            self.printer.clear_screen()
            server_names = [server.name for server in self.mcp_servers]
            self.printer.print_welcome(server_names, self.session_start)
            return True

        return False

    def _show_help(self):
        """Show help information."""
        help_data = [
            {"Command": "help", "Description": "Show this help message"},
            {"Command": "tools", "Description": "List available tools from MCP servers"},
            {"Command": "history", "Description": "Show conversation history"},
            {"Command": "clear", "Description": "Clear the screen"},
            {"Command": "save", "Description": "Save conversation to a file"},
            {"Command": "quit/exit", "Description": "End the chat session"},
        ]

        self.printer.print_table(help_data, "Available Commands")
        self.printer.print_info("Type any other text to chat with the AI assistant")

    def _show_history(self):
        """Show conversation history."""
        if not self.conversation_history:
            self.printer.print_info("No conversation history yet.")
            return

        self.printer.print_rule(f"Conversation History ({len(self.conversation_history)} interactions)")

        for i, interaction in enumerate(self.conversation_history, 1):
            timestamp = datetime.fromisoformat(interaction["timestamp"]).strftime('%H:%M:%S')

            self.printer.print_info(f"[{i}] {timestamp}")
            self.printer.print_user_input(interaction['user'])

            # Truncate long responses for history view
            response = interaction['assistant']
            if len(response) > 200:
                response = response[:200] + "..."

            self.printer.print_assistant_response(response)
            self.printer.print_rule()

    def _save_conversation(self):
        """Save conversation to file."""
        filename = f"chat_session_{self.session_start.strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(filename, 'w') as f:
                json.dump({
                    "session_start": self.session_start.isoformat(),
                    "mcp_servers": [server.name for server in self.mcp_servers],
                    "conversation": self.conversation_history
                }, f, indent=2)
            self.printer.print_success(f"Conversation saved to: {filename}")
        except Exception as e:
            self.printer.print_error(f"Error saving conversation: {e}")

    async def chat_loop(self):
        """Main chat loop with beautiful formatting."""
        server_names = await self.initialize_agent()
        self.printer.print_welcome(server_names, self.session_start)

        self.printer.print_info("Ready to help! What would you like to know or do?")
        self.printer.print_rule()

        while True:
            try:
                # Get user input with a nice prompt
                user_input = input("💬 You: ").strip()

                if not user_input:
                    continue

                # Handle special commands
                if await self.handle_special_commands(user_input):
                    if user_input.lower().strip() in ['quit', 'exit', 'bye']:
                        break
                    continue

                # Process chat message with tool call tracking
                try:
                    result = await self.enhanced_runner.run_with_tool_tracking(self.agent, user_input)
                    response = result.final_output
                    self.printer.print_assistant_response(response)

                    # Add to history
                    self.add_to_history(user_input, response)

                except Exception as e:
                    error_msg = f"Sorry, I encountered an error: {e}"
                    self.printer.print_error(error_msg)
                    self.add_to_history(user_input, error_msg)

                self.printer.print_rule()

            except KeyboardInterrupt:
                self.printer.print_rule()
                self.printer.print_info(f"Chat interrupted after {len(self.conversation_history)} interactions.")
                self.printer.print_success("Goodbye! 👋")
                break
            except EOFError:
                self.printer.print_rule()
                self.printer.print_info(f"Chat ended after {len(self.conversation_history)} interactions.")
                self.printer.print_success("Goodbye! 👋")
                break


async def main():
    """Main function that sets up MCP servers and starts the pretty chat client."""

    parser = argparse.ArgumentParser(description="Pretty Chat Client with Beautiful Formatting")
    parser.add_argument(
        "--config",
        default="mcp_config.json",
        help="Path to MCP configuration JSON file (default: mcp_config.json)"
    )
    parser.add_argument(
        "--no-rich",
        action="store_true",
        help="Disable rich formatting (use simple text formatting)"
    )

    args = parser.parse_args()

    # Get the directory of this script for resolving relative paths
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = args.config

    # If config path is relative, resolve it relative to the script directory
    if not os.path.isabs(config_path):
        config_path = os.path.join(current_dir, config_path)

    try:
        print(f"🔧 Loading MCP servers from: {config_path}")
        mcp_servers = load_mcp_servers_from_config(config_path, current_dir)

        if not mcp_servers:
            print("❌ No enabled MCP servers found in configuration.")
            return 1

        print(f"✅ Successfully loaded {len(mcp_servers)} MCP server(s)")

        # Start all servers and run chat client
        use_rich = not args.no_rich

        if len(mcp_servers) == 1:
            async with mcp_servers[0] as server:
                chat_client = PrettyChatClient([server], use_rich=use_rich)
                await chat_client.chat_loop()
        elif len(mcp_servers) == 2:
            async with mcp_servers[0] as server1, mcp_servers[1] as server2:
                chat_client = PrettyChatClient([server1, server2], use_rich=use_rich)
                await chat_client.chat_loop()
        else:
            # For more servers, handle them sequentially
            print("⚠️  Note: Running with multiple servers (sequential mode)")
            for server in mcp_servers:
                async with server:
                    chat_client = PrettyChatClient([server], use_rich=use_rich)
                    await chat_client.chat_loop()
                    break  # Just use the first server for now

    except Exception as e:
        print(f"❌ Error: {e}")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code or 0)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
