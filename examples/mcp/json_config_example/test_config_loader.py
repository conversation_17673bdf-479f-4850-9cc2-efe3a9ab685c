#!/usr/bin/env python3
"""
Test script to validate MCP configuration loading.

This script tests the MCPConfigLoader without actually running the servers,
useful for debugging configuration issues.
"""

import os
import sys
from mcp_loader import MCPConfigLoader


def test_config_loading(config_path: str):
    """Test loading and parsing of MCP configuration."""
    
    print(f"Testing configuration file: {config_path}")
    print("-" * 50)
    
    try:
        # Test loading the configuration
        loader = MCPConfigLoader(config_path)
        print("✓ Configuration file loaded successfully")
        
        # Print the raw configuration
        print(f"Raw configuration: {loader.config}")
        print()
        
        # Test getting enabled servers
        enabled_servers = loader.get_enabled_servers()
        print(f"Found {len(enabled_servers)} server(s):")
        
        for i, server in enumerate(enabled_servers, 1):
            print(f"  {i}. {server['name']}")
            print(f"     Command: {server['command']}")
            print(f"     Args: {server['args']}")
            print(f"     Env: {server['env']}")
            print()
        
        # Test creating server instances (without starting them)
        print("Testing server creation...")
        servers = loader.create_mcp_servers()
        print(f"✓ Successfully created {len(servers)} server instance(s)")
        
        for server in servers:
            print(f"  - {server.name}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False


def main():
    """Main function."""
    
    if len(sys.argv) > 1:
        config_path = sys.argv[1]
    else:
        # Use default config
        current_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(current_dir, "mcp_config.json")
    
    if not os.path.exists(config_path):
        print(f"Configuration file not found: {config_path}")
        return 1
    
    success = test_config_loading(config_path)
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
