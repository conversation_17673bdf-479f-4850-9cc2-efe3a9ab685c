#!/bin/bash

# Setup and Run Script for MCP Chat Client
# This script sets up the environment and runs the chat client

echo "🚀 MCP Chat Client Setup and Launch"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "chat_client.py" ]; then
    echo "❌ Error: Please run this script from the json_config_example directory"
    echo "   cd examples/mcp/json_config_example"
    echo "   ./setup_and_run.sh"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv .venv
    if [ $? -ne 0 ]; then
        echo "❌ Failed to create virtual environment"
        exit 1
    fi
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source .venv/bin/activate

# Install dependencies
echo "📥 Installing dependencies..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚙️  Setting up environment file..."
    cp .env_sample .env
    echo "✅ Created .env file from .env_sample"
    echo "   Your Azure OpenAI credentials are already configured!"
else
    echo "✅ Environment file already exists"
fi

# Check if npx is available
if ! command -v npx &> /dev/null; then
    echo "⚠️  Warning: npx not found. Some MCP servers may not work."
    echo "   Install with: npm install -g npx"
fi

# Check if uv is available (for ControlPlaneServer)
if ! command -v uv &> /dev/null; then
    echo "⚠️  Warning: uv not found. ControlPlaneServer may not work."
    echo "   Install with: curl -LsSf https://astral.sh/uv/install.sh | sh"
fi

echo ""
echo "✅ Setup complete!"
echo ""

# Ask which client to run
echo "Choose your chat client:"
echo "1. Basic Chat Client"
echo "2. Enhanced Chat Client (recommended)"
echo "3. Quick launcher (auto-detects your config)"
echo ""

read -p "Enter your choice (1-3, default: 3): " choice

case $choice in
    1)
        echo "🎯 Starting Basic Chat Client..."
        python chat_client.py
        ;;
    2)
        echo "🎯 Starting Enhanced Chat Client..."
        python enhanced_chat_client.py
        ;;
    3|"")
        echo "🎯 Starting Quick Launcher..."
        python start_chat.py
        ;;
    *)
        echo "❌ Invalid choice. Starting Enhanced Chat Client..."
        python enhanced_chat_client.py
        ;;
esac

echo ""
echo "👋 Thanks for using the MCP Chat Client!"
