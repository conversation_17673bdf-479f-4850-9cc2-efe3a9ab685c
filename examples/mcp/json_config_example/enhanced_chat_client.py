#!/usr/bin/env python3
"""
Enhanced Interactive Chat Client with MCP Server Support

This is an enhanced version of the chat client with features like:
- Conversation history
- Better formatting
- Session management
- Improved error handling
"""

import asyncio
import os
import argparse
import sys
import json
from datetime import datetime
from typing import List, Dict, Any

from agents import Agent, OpenAIChatCompletionsModel, Runner, set_tracing_disabled
from agents.mcp import MCPServer
from dotenv import load_dotenv
from openai import AsyncAzure<PERSON>penAI

from mcp_loader import load_mcp_servers_from_config


class EnhancedChatClient:
    """Enhanced interactive chat client with conversation history and better UX."""
    
    def __init__(self, mcp_servers: List[MCPServer]):
        """Initialize the enhanced chat client with MCP servers."""
        self.mcp_servers = mcp_servers
        self.agent = None
        self.conversation_history = []
        self.session_start = datetime.now()
        
    def get_azure_open_ai_client(self):
        """Create and return Azure OpenAI client instance."""
        load_dotenv()
        
        return AsyncAzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
        )
    
    async def initialize_agent(self):
        """Initialize the AI agent with MCP servers."""
        azure_open_ai_client = self.get_azure_open_ai_client()
        set_tracing_disabled(disabled=True)

        # Create agent with all MCP servers
        self.agent = Agent(
            name="Assistant",
            instructions="""You are a helpful AI assistant with access to various tools through MCP servers. 

You should:
- Be conversational, friendly, and helpful
- Use available tools when they can help answer questions or perform tasks
- Explain what you're doing when using tools
- Provide clear, well-formatted responses
- Ask clarifying questions when needed

Available capabilities depend on the configured MCP servers, which may include:
- File system operations (reading, writing, listing files)
- Control plane management and operations
- Database queries and management
- Web searches and data retrieval
- And more depending on your specific configuration

Always be transparent about what tools you're using and what information you're finding.
            """,
            model=OpenAIChatCompletionsModel(
                model=os.getenv("AZURE_OPENAI_CHAT_DEPLOYMENT_NAME"), 
                openai_client=azure_open_ai_client
            ),
            mcp_servers=self.mcp_servers,
        )
        
        print(f"🤖 Agent initialized with {len(self.mcp_servers)} MCP server(s):")
        for server in self.mcp_servers:
            print(f"   • {server.name}")
        print()
    
    def print_welcome(self):
        """Print welcome message and instructions."""
        print("=" * 70)
        print("🚀 Enhanced Interactive Chat Client with MCP Server Support")
        print("=" * 70)
        print(f"📅 Session started: {self.session_start.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        print("💬 Welcome! I'm your AI assistant with access to powerful tools.")
        print("🔧 I can help you with various tasks using the configured MCP servers.")
        print()
        print("💡 Tips:")
        print("   • Ask me to list available tools to see what I can do")
        print("   • I can read files, manage resources, search data, and more")
        print("   • Be specific about what you want to accomplish")
        print()
        print("🎮 Commands:")
        print("   • 'help' - Show available commands")
        print("   • 'tools' - List available tools")
        print("   • 'history' - Show conversation history")
        print("   • 'clear' - Clear the screen")
        print("   • 'save' - Save conversation to file")
        print("   • 'quit' or 'exit' - End the session")
        print()
        print("-" * 70)
    
    def add_to_history(self, user_input: str, assistant_response: str):
        """Add interaction to conversation history."""
        self.conversation_history.append({
            "timestamp": datetime.now().isoformat(),
            "user": user_input,
            "assistant": assistant_response
        })
    
    async def handle_special_commands(self, user_input: str) -> bool:
        """Handle special commands. Returns True if command was handled."""
        command = user_input.lower().strip()
        
        if command in ['quit', 'exit', 'bye']:
            print(f"\n👋 Session ended after {len(self.conversation_history)} interactions.")
            print("Thanks for chatting! Goodbye!")
            return True
            
        elif command == 'help':
            print("\n📖 Available Commands:")
            print("   • help - Show this help message")
            print("   • tools - List available tools from MCP servers")
            print("   • history - Show conversation history")
            print("   • clear - Clear the screen")
            print("   • save - Save conversation to a file")
            print("   • quit/exit - End the chat session")
            print("   • Any other text - Chat with the AI assistant")
            print()
            return True
            
        elif command == 'tools':
            print("\n🔧 Requesting available tools from the assistant...")
            try:
                result = await Runner.run(
                    starting_agent=self.agent, 
                    input="What tools do you have available? Please list them with brief descriptions of what each tool can do."
                )
                response = result.final_output
                print(f"\n🤖 Assistant: {response}")
                self.add_to_history("tools", response)
            except Exception as e:
                error_msg = f"Error getting tools: {e}"
                print(f"❌ {error_msg}")
                self.add_to_history("tools", error_msg)
            print()
            return True
            
        elif command == 'history':
            print(f"\n📚 Conversation History ({len(self.conversation_history)} interactions):")
            print("-" * 50)
            for i, interaction in enumerate(self.conversation_history, 1):
                timestamp = datetime.fromisoformat(interaction["timestamp"]).strftime('%H:%M:%S')
                print(f"\n[{i}] {timestamp}")
                print(f"You: {interaction['user']}")
                print(f"Assistant: {interaction['assistant'][:100]}{'...' if len(interaction['assistant']) > 100 else ''}")
            print()
            return True
            
        elif command == 'save':
            filename = f"chat_session_{self.session_start.strftime('%Y%m%d_%H%M%S')}.json"
            try:
                with open(filename, 'w') as f:
                    json.dump({
                        "session_start": self.session_start.isoformat(),
                        "mcp_servers": [server.name for server in self.mcp_servers],
                        "conversation": self.conversation_history
                    }, f, indent=2)
                print(f"\n💾 Conversation saved to: {filename}")
            except Exception as e:
                print(f"❌ Error saving conversation: {e}")
            print()
            return True
            
        elif command == 'clear':
            os.system('clear' if os.name == 'posix' else 'cls')
            self.print_welcome()
            return True
            
        return False
    
    def format_response(self, response: str) -> str:
        """Format the assistant's response for better readability."""
        # Add some basic formatting improvements
        lines = response.split('\n')
        formatted_lines = []
        
        for line in lines:
            # Add emoji for lists
            if line.strip().startswith('- '):
                line = line.replace('- ', '• ')
            elif line.strip().startswith('* '):
                line = line.replace('* ', '• ')
            formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)
    
    async def chat_loop(self):
        """Main chat loop with enhanced features."""
        self.print_welcome()
        
        print("🎯 Ready to help! What would you like to know or do?")
        print()
        
        while True:
            try:
                # Get user input with a nice prompt
                user_input = input("💬 You: ").strip()
                
                if not user_input:
                    continue
                
                # Handle special commands
                if await self.handle_special_commands(user_input):
                    if user_input.lower().strip() in ['quit', 'exit', 'bye']:
                        break
                    continue
                
                # Process chat message
                print("\n🤖 Assistant: ", end="", flush=True)
                
                try:
                    result = await Runner.run(starting_agent=self.agent, input=user_input)
                    response = self.format_response(result.final_output)
                    print(response)
                    
                    # Add to history
                    self.add_to_history(user_input, result.final_output)
                    
                except Exception as e:
                    error_msg = f"Sorry, I encountered an error: {e}"
                    print(f"❌ {error_msg}")
                    self.add_to_history(user_input, error_msg)
                
                print()
                
            except KeyboardInterrupt:
                print(f"\n\n👋 Chat interrupted after {len(self.conversation_history)} interactions. Goodbye!")
                break
            except EOFError:
                print(f"\n\n👋 Chat ended after {len(self.conversation_history)} interactions. Goodbye!")
                break


async def main():
    """Main function that sets up MCP servers and starts the enhanced chat client."""
    
    parser = argparse.ArgumentParser(description="Enhanced Interactive Chat Client with MCP Server Support")
    parser.add_argument(
        "--config", 
        default="mcp_config.json",
        help="Path to MCP configuration JSON file (default: mcp_config.json)"
    )
    
    args = parser.parse_args()
    
    # Get the directory of this script for resolving relative paths
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = args.config
    
    # If config path is relative, resolve it relative to the script directory
    if not os.path.isabs(config_path):
        config_path = os.path.join(current_dir, config_path)
    
    try:
        print(f"🔧 Loading MCP servers from: {config_path}")
        mcp_servers = load_mcp_servers_from_config(config_path, current_dir)
        
        if not mcp_servers:
            print("❌ No enabled MCP servers found in configuration.")
            return 1
            
        print(f"✅ Successfully loaded {len(mcp_servers)} MCP server(s)")
        
        # Start all servers and run chat client
        if len(mcp_servers) == 1:
            async with mcp_servers[0] as server:
                chat_client = EnhancedChatClient([server])
                await chat_client.initialize_agent()
                await chat_client.chat_loop()
        elif len(mcp_servers) == 2:
            async with mcp_servers[0] as server1, mcp_servers[1] as server2:
                chat_client = EnhancedChatClient([server1, server2])
                await chat_client.initialize_agent()
                await chat_client.chat_loop()
        else:
            # For more servers, handle them sequentially
            print("⚠️  Note: Running with multiple servers (sequential mode)")
            for server in mcp_servers:
                async with server:
                    chat_client = EnhancedChatClient([server])
                    await chat_client.initialize_agent()
                    await chat_client.chat_loop()
                    break  # Just use the first server for now
                    
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code or 0)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
