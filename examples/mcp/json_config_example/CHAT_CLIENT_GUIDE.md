# 💬 Chat Client Guide

This guide shows you how to run the interactive chat client with your MCP server configuration.

## 🚀 Quick Start (Easiest Way)

### Option 1: Super Simple (Just Chat!)
```bash
cd examples/mcp/json_config_example
python chat.py
```

This directly starts the pretty chat client with your MCP configuration - no setup needed!

### Option 2: One-Command Setup and Run
```bash
cd examples/mcp/json_config_example
./setup_and_run.sh
```

This script will:
- ✅ Create virtual environment
- ✅ Install dependencies
- ✅ Set up environment variables (using your Azure OpenAI credentials)
- ✅ Launch the pretty chat client (now default)

### Option 3: Launcher with Custom Config
```bash
cd examples/mcp/json_config_example
python start_chat.py /path/to/your/mcp.json
```

This automatically uses the pretty chat client with any MCP config file.

### Option 4: See Demos
```bash
# Pretty formatting demo
python demo_pretty_printing.py

# Tool call tracking demo
python demo_tool_tracking.py
```

These show you how the pretty formatting and tool call tracking work before starting a chat session.

## 🎯 Manual Setup and Run

### 1. Setup Environment
```bash
cd examples/mcp/json_config_example

# Create virtual environment
python3 -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Setup environment file (your credentials are already included)
cp .env_sample .env
```

### 2. Choose Your Chat Client

#### Basic Chat Client
```bash
python chat_client.py --config /Users/<USER>/dev/personal/local_llm/docker_compose_ollama/mcp.json
```

#### Enhanced Chat Client
```bash
python enhanced_chat_client.py --config /Users/<USER>/dev/personal/local_llm/docker_compose_ollama/mcp.json
```

#### Pretty Chat Client (Recommended)
```bash
python pretty_chat_client.py --config /Users/<USER>/dev/personal/local_llm/docker_compose_ollama/mcp.json
```

## 🎮 Chat Client Features

### Basic Chat Client
- ✅ Interactive chat with AI assistant
- ✅ Access to MCP server tools
- ✅ Basic commands (help, tools, quit)
- ✅ Real-time responses

### Enhanced Chat Client
- ✅ All basic features
- ✅ Conversation history
- ✅ Save conversations to file
- ✅ Better formatting and UX
- ✅ Session management
- ✅ More commands (history, save, clear)

### Pretty Chat Client (Recommended)
- ✅ All enhanced features
- ✅ **Beautiful syntax highlighting** (like Claude CLI)
- ✅ **Rich markdown rendering** with proper formatting
- ✅ **Code blocks with language detection**
- ✅ **Structured tables and lists**
- ✅ **Color-coded messages** (info, success, warning, error)
- ✅ **Thinking indicators** and progress feedback
- ✅ **Panel layouts** and visual separators
- ✅ **Real-time tool call tracking** - See which MCP tools are being called
- ✅ **Tool execution feedback** - Success/failure indicators with results

## 💡 Chat Commands

Once in the chat, you can use these commands:

- **`help`** - Show available commands
- **`tools`** - List available tools from your MCP servers
- **`history`** - Show conversation history (enhanced client only)
- **`save`** - Save conversation to file (enhanced client only)
- **`clear`** - Clear the screen
- **`quit`** or **`exit`** - End the chat session

## 🔧 Your MCP Configuration

Your configuration file includes:
```json
{
  "mcpServers": {
    "ControlPlaneServer": {
      "command": "uv",
      "args": [
        "run",
        "--directory",
        "/Users/<USER>/dev/work/repos/control-plane-mcp-server",
        "main.py"
      ],
      "env": {
        "FACETS_PROFILE": "facetsdemo"
      }
    }
  }
}
```

This gives the AI assistant access to your Control Plane Server tools!

## 🎯 Example Chat Session

```
💬 You: What tools do you have available?

🤖 Assistant: I have access to tools from the ControlPlaneServer which provides
control plane management capabilities. Let me list the specific tools...

💬 You: Can you help me manage my resources?

🤖 Assistant: Absolutely! I can help you with resource management through the
control plane tools. What specific resources would you like to work with?
```

## 🛠️ Troubleshooting

### "uv not found" error
```bash
# Install uv
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### "npx not found" error
```bash
# Install Node.js and npm, then:
npm install -g npx
```

### Azure OpenAI connection issues
- Check your `.env` file has the correct credentials
- Verify your Azure OpenAI deployment is active
- Make sure the model name matches your deployment

### MCP server connection issues
- Ensure the control plane server path is correct
- Check that the `FACETS_PROFILE` environment variable is set correctly
- Verify the control plane server is accessible

## 🎉 Tips for Best Experience

1. **Use the Enhanced Chat Client** - It has better features and UX
2. **Start with `tools` command** - See what capabilities you have
3. **Be specific in your requests** - The AI works better with clear instructions
4. **Use `save` command** - Keep important conversations
5. **Try different types of queries** - File operations, resource management, etc.

## 📁 File Structure

```
examples/mcp/json_config_example/
├── chat.py                     # Super simple launcher (just run this!)
├── chat_client.py              # Basic chat client
├── enhanced_chat_client.py     # Enhanced chat client with history
├── pretty_chat_client.py       # Pretty chat client with beautiful formatting (default)
├── pretty_printer.py           # Pretty printing utility module
├── tool_call_tracker.py        # Tool call tracking and interception
├── demo_pretty_printing.py     # Demo of pretty printing features
├── demo_tool_tracking.py       # Demo of tool call tracking
├── start_chat.py              # Advanced launcher with options
├── setup_and_run.sh           # One-command setup and run
├── mcp_loader.py              # MCP configuration loader
├── mcp_config.json            # Example configuration
├── .env_sample                # Environment template (with your credentials)
└── requirements.txt           # Python dependencies (includes Rich and Pygments)
```

Now you have a powerful chat interface to interact with your MCP servers! 🚀
