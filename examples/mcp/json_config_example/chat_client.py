#!/usr/bin/env python3
"""
Interactive Chat Client with MCP Server Support

This script creates an interactive chat interface that connects to MCP servers
loaded from a JSON configuration file. Users can chat with the AI assistant
which has access to tools provided by the configured MCP servers.
"""

import asyncio
import os
import argparse
import sys
from typing import List

from agents import Agent, OpenAIChatCompletionsModel, Runner, set_tracing_disabled
from agents.mcp import MCPServer
from dotenv import load_dotenv
from openai import AsyncAzureOpenAI

from mcp_loader import load_mcp_servers_from_config


class ChatClient:
    """Interactive chat client with MCP server support."""

    def __init__(self, mcp_servers: List[MCPServer]):
        """Initialize the chat client with MCP servers."""
        self.mcp_servers = mcp_servers
        self.agent = None

    def get_azure_open_ai_client(self):
        """Create and return Azure OpenAI client instance."""
        load_dotenv()

        return AsyncAzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
        )

    async def initialize_agent(self):
        """Initialize the AI agent with MCP servers."""
        azure_open_ai_client = self.get_azure_open_ai_client()
        set_tracing_disabled(disabled=True)

        # Create agent with all MCP servers
        self.agent = Agent(
            name="Assistant",
            instructions="""You are a helpful AI assistant with access to various tools through MCP servers.

Use the available tools to help answer user questions and perform tasks. Be conversational and helpful.
When using tools, explain what you're doing and what information you found.

Available capabilities depend on the configured MCP servers, which may include:
- File system operations
- Control plane management
- Database queries
- Web searches
- And more depending on configuration
            """,
            model=OpenAIChatCompletionsModel(
                model=os.getenv("AZURE_OPENAI_CHAT_DEPLOYMENT_NAME"),
                openai_client=azure_open_ai_client
            ),
            mcp_servers=self.mcp_servers,
        )

        print(f"🤖 Agent initialized with {len(self.mcp_servers)} MCP server(s):")
        for server in self.mcp_servers:
            print(f"   • {server.name}")
        print()

    def print_welcome(self):
        """Print welcome message and instructions."""
        print("=" * 60)
        print("🚀 Interactive Chat Client with MCP Server Support")
        print("=" * 60)
        print()
        print("💬 Start chatting with the AI assistant!")
        print("🔧 The assistant has access to tools from your MCP servers")
        print()
        print("Commands:")
        print("  • Type your message and press Enter to chat")
        print("  • Type 'help' for available commands")
        print("  • Type 'tools' to see available tools")
        print("  • Type '/q' or 'quit' to end the session")
        print("  • Press Ctrl+C to force quit")
        print()
        print("-" * 60)

    async def handle_special_commands(self, user_input: str) -> bool:
        """Handle special commands. Returns True if command was handled."""
        command = user_input.lower().strip()

        if command in ['quit', 'exit', 'bye', '/q', 'q']:
            print("\n👋 Thanks for chatting! Goodbye!")
            return True

        elif command == 'help':
            print("\n📖 Available Commands:")
            print("  • help - Show this help message")
            print("  • tools - List available tools from MCP servers")
            print("  • clear - Clear the screen")
            print("  • /q or quit - End the chat session (also Ctrl+C)")
            print("  • Any other text - Chat with the AI assistant")
            print("  💡 Quick tip: Use /q to quit quickly")
            print()
            return True

        elif command == 'tools':
            print("\n🔧 Requesting available tools from the assistant...")
            try:
                result = await Runner.run(
                    starting_agent=self.agent,
                    input="What tools do you have available? Please list them with brief descriptions."
                )
                print(f"\n🤖 Assistant: {result.final_output}")
            except Exception as e:
                print(f"❌ Error getting tools: {e}")
            print()
            return True

        elif command == 'clear':
            os.system('clear' if os.name == 'posix' else 'cls')
            self.print_welcome()
            return True

        return False

    async def chat_loop(self):
        """Main chat loop."""
        self.print_welcome()

        print("🎯 Ready to chat! What would you like to know or do?")
        print()

        while True:
            try:
                # Get user input
                user_input = input("💬 You: ").strip()

                if not user_input:
                    continue

                # Handle special commands
                if await self.handle_special_commands(user_input):
                    if user_input.lower().strip() in ['quit', 'exit', 'bye', '/q', 'q']:
                        break
                    continue

                # Process chat message
                print("\n🤖 Assistant: ", end="", flush=True)

                try:
                    result = await Runner.run(starting_agent=self.agent, input=user_input)
                    print(result.final_output)
                except Exception as e:
                    print(f"❌ Sorry, I encountered an error: {e}")

                print()

            except KeyboardInterrupt:
                print("\n\n👋 Chat interrupted with Ctrl+C. Thanks for chatting! Goodbye!")
                break
            except EOFError:
                print("\n\n👋 Chat ended. Thanks for chatting! Goodbye!")
                break


async def main():
    """Main function that sets up MCP servers and starts the chat client."""

    parser = argparse.ArgumentParser(description="Interactive Chat Client with MCP Server Support")
    parser.add_argument(
        "--config",
        default="mcp_config.json",
        help="Path to MCP configuration JSON file (default: mcp_config.json)"
    )

    args = parser.parse_args()

    # Get the directory of this script for resolving relative paths
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = args.config

    # If config path is relative, resolve it relative to the script directory
    if not os.path.isabs(config_path):
        config_path = os.path.join(current_dir, config_path)

    try:
        print(f"🔧 Loading MCP servers from: {config_path}")
        mcp_servers = load_mcp_servers_from_config(config_path, current_dir)

        if not mcp_servers:
            print("❌ No enabled MCP servers found in configuration.")
            return 1

        print(f"✅ Successfully loaded {len(mcp_servers)} MCP server(s)")

        # Start all servers and run chat client
        if len(mcp_servers) == 1:
            async with mcp_servers[0] as server:
                chat_client = ChatClient([server])
                await chat_client.initialize_agent()
                await chat_client.chat_loop()
        elif len(mcp_servers) == 2:
            async with mcp_servers[0] as server1, mcp_servers[1] as server2:
                chat_client = ChatClient([server1, server2])
                await chat_client.initialize_agent()
                await chat_client.chat_loop()
        else:
            # For more servers, handle them sequentially
            print("⚠️  Note: Running with multiple servers (sequential mode)")
            for server in mcp_servers:
                async with server:
                    chat_client = ChatClient([server])
                    await chat_client.initialize_agent()
                    await chat_client.chat_loop()
                    break  # Just use the first server for now

    except Exception as e:
        print(f"❌ Error: {e}")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code or 0)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
