#!/usr/bin/env python3
"""
Demo script to test tool call tracking functionality.

This script demonstrates how tool calls are intercepted and displayed
in the pretty chat client.
"""

import asyncio
from datetime import datetime
from pretty_printer import Pretty<PERSON><PERSON><PERSON>
from tool_call_tracker import <PERSON>l<PERSON>allT<PERSON>, MCPToolCallInterceptor


class MockMCPServer:
    """Mock MCP server for testing tool call interception."""
    
    def __init__(self, name: str):
        """Initialize mock server."""
        self.name = name
    
    async def call_tool(self, tool_name: str, arguments: dict = None):
        """Mock tool call that simulates real MCP behavior."""
        # Simulate some processing time
        await asyncio.sleep(0.5)
        
        # Mock different types of results
        if tool_name == "list_tools":
            return MockResult([
                MockContent("Available tools: read_file, write_file, list_directory")
            ])
        elif tool_name == "read_file":
            filename = arguments.get("path", "unknown") if arguments else "unknown"
            return MockResult([
                MockContent(f"File content from {filename}: Hello, <PERSON>!")
            ])
        elif tool_name == "list_directory":
            return MockResult([
                Mock<PERSON>ontent("Files: file1.txt, file2.py, config.json")
            ])
        else:
            return MockResult([
                MockContent(f"Tool {tool_name} executed successfully")
            ])


class MockResult:
    """Mock MCP result."""
    
    def __init__(self, content):
        self.content = content


class MockContent:
    """Mock MCP content item."""
    
    def __init__(self, text):
        self.text = text


async def demo_tool_tracking():
    """Demonstrate tool call tracking."""
    
    printer = PrettyPrinter(use_rich=True)
    
    # Print demo header
    printer.print_rule("Tool Call Tracking Demo")
    printer.print_info("This demo shows how MCP tool calls are intercepted and displayed")
    print()
    
    # Create mock server and interceptor
    mock_server = MockMCPServer("Demo Control Plane Server")
    interceptor = MCPToolCallInterceptor(printer)
    
    # Patch the server
    interceptor.patch_mcp_server(mock_server)
    
    # Demo various tool calls
    demo_calls = [
        ("list_tools", None),
        ("read_file", {"path": "/etc/config.json"}),
        ("list_directory", {"path": "/home/<USER>"}),
        ("create_resource", {"name": "test-resource", "type": "service"}),
        ("get_status", {"resource": "test-resource"}),
    ]
    
    for tool_name, args in demo_calls:
        print(f"💬 User: Can you {tool_name.replace('_', ' ')}?")
        printer.print_thinking()
        
        try:
            # This will trigger the intercepted call_tool method
            result = await mock_server.call_tool(tool_name, args)
            
            # Simulate assistant response
            response = f"I've successfully executed the {tool_name} tool. "
            if hasattr(result, 'content') and result.content:
                response += f"Result: {result.content[0].text}"
            
            printer.print_assistant_response(response)
            
        except Exception as e:
            printer.print_error(f"Error calling {tool_name}: {e}")
        
        printer.print_rule()
        await asyncio.sleep(1)  # Brief pause between demos
    
    # Cleanup
    interceptor.unpatch_mcp_server(mock_server)
    
    printer.print_success("Tool call tracking demo completed!")
    printer.print_info("This is how tool calls will be displayed in the pretty chat client")


async def demo_multiple_tools():
    """Demo multiple simultaneous tool calls."""
    
    printer = PrettyPrinter(use_rich=True)
    
    printer.print_rule("Multiple Tool Calls Demo")
    
    # Create multiple mock servers
    servers = [
        MockMCPServer("Control Plane Server"),
        MockMCPServer("Filesystem Server"),
        MockMCPServer("Database Server"),
    ]
    
    interceptor = MCPToolCallInterceptor(printer)
    
    # Patch all servers
    for server in servers:
        interceptor.patch_mcp_server(server)
    
    print("💬 User: Can you check the status of all systems?")
    printer.print_thinking()
    
    # Simulate multiple concurrent tool calls
    tasks = []
    for i, server in enumerate(servers):
        tool_name = ["get_status", "list_files", "query_database"][i]
        task = asyncio.create_task(server.call_tool(tool_name))
        tasks.append(task)
    
    # Wait for all to complete
    results = await asyncio.gather(*tasks)
    
    response = "I've checked all systems:\n"
    for i, result in enumerate(results):
        server_name = servers[i].name
        response += f"• {server_name}: {result.content[0].text}\n"
    
    printer.print_assistant_response(response)
    
    # Cleanup
    for server in servers:
        interceptor.unpatch_mcp_server(server)
    
    printer.print_success("Multiple tool calls demo completed!")


def main():
    """Main demo function."""
    
    print("🔧 Tool Call Tracking Demo")
    print("=" * 40)
    print()
    print("This demo shows how MCP tool calls are intercepted and displayed")
    print("in the pretty chat client, similar to Claude CLI and mcphost.")
    print()
    
    choice = input("Choose demo (1=Single tools, 2=Multiple tools, 3=Both, default=3): ").strip()
    
    async def run_demos():
        if choice in ["1", ""]:
            await demo_tool_tracking()
        elif choice == "2":
            await demo_multiple_tools()
        else:  # choice == "3" or default
            await demo_tool_tracking()
            print("\n" + "="*60 + "\n")
            await demo_multiple_tools()
    
    try:
        asyncio.run(run_demos())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted. Goodbye!")


if __name__ == "__main__":
    main()
