"""
Tool Call Tracker for MCP Chat Client

This module provides functionality to track and display MCP tool calls
in real-time, similar to Claude <PERSON> and mcphost.
"""

import asyncio
import json
import re
from typing import Any, Dict, List, Optional
from datetime import datetime

from agents import Agent, Runner
from pretty_printer import PrettyPrinter


class ToolCallTracker:
    """Tracks and displays MCP tool calls in real-time."""

    def __init__(self, printer: PrettyPrinter):
        """Initialize the tool call tracker."""
        self.printer = printer
        self.active_tools = {}
        self.tool_call_count = 0

    def extract_tool_calls_from_response(self, response_text: str) -> List[Dict[str, Any]]:
        """Extract tool call information from response text."""
        # This is a simplified approach - in a real implementation,
        # you'd want to hook into the actual tool calling mechanism
        tool_calls = []

        # Look for patterns that indicate tool usage
        patterns = [
            r"calling\s+(\w+)",
            r"using\s+(\w+)\s+tool",
            r"tool:\s*(\w+)",
            r"function:\s*(\w+)",
        ]

        for pattern in patterns:
            matches = re.findall(pattern, response_text, re.IGNORECASE)
            for match in matches:
                tool_calls.append({
                    "name": match,
                    "server": "unknown",
                    "args": {}
                })

        return tool_calls

    def start_tool_call(self, tool_name: str, server_name: str = "", args: Dict[str, Any] = None):
        """Display the start of a tool call."""
        self.tool_call_count += 1
        call_id = f"call_{self.tool_call_count}"

        self.active_tools[call_id] = {
            "name": tool_name,
            "server": server_name,
            "start_time": datetime.now(),
            "args": args or {}
        }

        self.printer.print_tool_call(tool_name, server_name, args)
        return call_id

    def end_tool_call(self, call_id: str, success: bool = True, result: Any = None):
        """Display the end of a tool call."""
        if call_id not in self.active_tools:
            return

        tool_info = self.active_tools[call_id]
        tool_name = tool_info["name"]

        # Create a summary of the result
        result_summary = ""
        if result:
            if isinstance(result, str):
                result_summary = result[:100]
            elif isinstance(result, dict):
                if "content" in result:
                    result_summary = str(result["content"])[:100]
                elif "result" in result:
                    result_summary = str(result["result"])[:100]
                else:
                    result_summary = f"Returned {len(result)} fields"
            elif isinstance(result, list):
                result_summary = f"Returned {len(result)} items"
            else:
                result_summary = str(result)[:100]

        self.printer.print_tool_result(tool_name, success, result_summary)

        # Clean up
        del self.active_tools[call_id]


class EnhancedRunner:
    """Enhanced runner that tracks tool calls via MCP interception."""

    def __init__(self, printer: PrettyPrinter):
        """Initialize the enhanced runner."""
        self.printer = printer

    async def run_with_tool_tracking(self, agent: Agent, input_text: str) -> Any:
        """Run the agent with tool call tracking."""

        # Show thinking indicator
        self.printer.print_thinking()

        try:
            # Run the actual agent - tool calls will be intercepted at MCP level
            result = await Runner.run(starting_agent=agent, input=input_text)
            return result

        except Exception as e:
            self.printer.print_error(f"Error during execution: {e}")
            raise


# Monkey patch approach to intercept actual tool calls
class MCPToolCallInterceptor:
    """Intercepts actual MCP tool calls for display."""

    def __init__(self, printer: PrettyPrinter):
        """Initialize the interceptor."""
        self.printer = printer
        self.tracker = ToolCallTracker(printer)
        self.original_methods = {}

    def patch_mcp_server(self, mcp_server):
        """Patch MCP server to intercept tool calls."""
        # Store original method - the correct method is call_tool
        if hasattr(mcp_server, 'call_tool'):
            self.original_methods[id(mcp_server)] = mcp_server.call_tool
            mcp_server.call_tool = self._create_intercepted_call_tool(mcp_server)

    def _create_intercepted_call_tool(self, mcp_server):
        """Create an intercepted version of call_tool."""
        original_call_tool = self.original_methods[id(mcp_server)]

        async def intercepted_call_tool(tool_name: str, arguments: Dict[str, Any] = None):
            """Intercepted call_tool method."""
            # Display tool call start
            call_id = self.tracker.start_tool_call(
                tool_name,
                getattr(mcp_server, 'name', 'MCP Server'),
                arguments
            )

            try:
                # Call original method
                result = await original_call_tool(tool_name, arguments)

                # Extract result summary for display
                result_summary = ""
                if hasattr(result, 'content') and result.content:
                    if len(result.content) == 1:
                        content_item = result.content[0]
                        if hasattr(content_item, 'text'):
                            result_summary = content_item.text[:100]
                        elif hasattr(content_item, 'data'):
                            result_summary = str(content_item.data)[:100]
                    else:
                        result_summary = f"Returned {len(result.content)} content items"

                # Display successful completion
                self.tracker.end_tool_call(call_id, success=True, result=result_summary)

                return result

            except Exception as e:
                # Display failed completion
                self.tracker.end_tool_call(call_id, success=False, result=str(e))
                raise

        return intercepted_call_tool

    def unpatch_mcp_server(self, mcp_server):
        """Restore original MCP server methods."""
        server_id = id(mcp_server)
        if server_id in self.original_methods:
            mcp_server.call_tool = self.original_methods[server_id]
            del self.original_methods[server_id]
