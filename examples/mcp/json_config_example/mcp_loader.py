"""
MCP Server Configuration Loader

This module provides utilities to load and configure MCP servers from JSON configuration files.
It supports environment variable substitution and multiple server types.
"""

import json
import os
from typing import Dict, List, Any, Optional
from agents.mcp import MCPServer, MCPServerStdio


class MCPConfigLoader:
    """Loads and manages MCP server configurations from JSON files."""

    def __init__(self, config_path: str, base_dir: Optional[str] = None):
        """
        Initialize the MCP configuration loader.

        Args:
            config_path: Path to the JSON configuration file
            base_dir: Base directory for resolving relative paths (defaults to config file directory)
        """
        self.config_path = config_path
        self.base_dir = base_dir or os.path.dirname(os.path.abspath(config_path))
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load and parse the JSON configuration file."""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            raise FileNotFoundError(f"MCP configuration file not found: {self.config_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file: {e}")



    def _resolve_path(self, path: str) -> str:
        """Resolve relative paths relative to the base directory."""
        if os.path.isabs(path):
            return path
        return os.path.join(self.base_dir, path)

    def get_enabled_servers(self) -> List[Dict[str, Any]]:
        """Get list of enabled server configurations."""
        mcp_servers = self.config.get('mcpServers', {})
        servers = []
        for name, config in mcp_servers.items():
            server_config = {
                'name': name,
                'command': config.get('command'),
                'args': config.get('args', []),
                'env': config.get('env', {})
            }
            servers.append(server_config)
        return servers

    def create_mcp_servers(self) -> List[MCPServer]:
        """
        Create MCP server instances from the configuration.

        Returns:
            List of configured MCP server instances
        """
        servers = []
        enabled_configs = self.get_enabled_servers()

        for server_config in enabled_configs:
            try:
                server = self._create_server(server_config)
                if server:
                    servers.append(server)
            except Exception as e:
                print(f"Warning: Failed to create server '{server_config.get('name', 'Unknown')}': {e}")

        return servers

    def _create_server(self, config: Dict[str, Any]) -> Optional[MCPServer]:
        """Create a single MCP server instance from configuration."""
        name = config.get('name', 'Unnamed Server')
        command = config.get('command')
        args = config.get('args', [])
        env = config.get('env', {})

        if not command:
            raise ValueError(f"No command specified for server {name}")

        # Resolve relative paths in args
        resolved_args = []
        for arg in args:
            if isinstance(arg, str) and arg.startswith('./'):
                resolved_args.append(self._resolve_path(arg))
            else:
                resolved_args.append(arg)

        params = {
            'command': command,
            'args': resolved_args,
            'env': env
        }

        return MCPServerStdio(name=name, params=params)


def load_mcp_servers_from_config(config_path: str, base_dir: Optional[str] = None) -> List[MCPServer]:
    """
    Convenience function to load MCP servers from a JSON configuration file.

    Args:
        config_path: Path to the JSON configuration file
        base_dir: Base directory for resolving relative paths

    Returns:
        List of configured MCP server instances
    """
    loader = MCPConfigLoader(config_path, base_dir)
    return loader.create_mcp_servers()
