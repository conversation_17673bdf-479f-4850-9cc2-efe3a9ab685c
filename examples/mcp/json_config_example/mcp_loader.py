"""
MCP Server Configuration Loader

This module provides utilities to load and configure MCP servers from JSON configuration files.
It supports environment variable substitution and multiple server types.
"""

import json
import os
import re
from typing import Dict, List, Any, Optional
from agents.mcp import MCPServer, MCPServerStdio


class MCPConfigLoader:
    """Loads and manages MCP server configurations from JSON files."""
    
    def __init__(self, config_path: str, base_dir: Optional[str] = None):
        """
        Initialize the MCP configuration loader.
        
        Args:
            config_path: Path to the JSON configuration file
            base_dir: Base directory for resolving relative paths (defaults to config file directory)
        """
        self.config_path = config_path
        self.base_dir = base_dir or os.path.dirname(os.path.abspath(config_path))
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load and parse the JSON configuration file."""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            raise FileNotFoundError(f"MCP configuration file not found: {self.config_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file: {e}")
    
    def _substitute_env_vars(self, value: str, env_vars: Dict[str, str]) -> str:
        """
        Substitute environment variables in a string.
        
        Supports ${VAR_NAME} syntax. First checks provided env_vars, then os.environ.
        """
        def replace_var(match):
            var_name = match.group(1)
            # Check provided env_vars first, then system environment
            if var_name in env_vars:
                return env_vars[var_name]
            elif var_name in os.environ:
                return os.environ[var_name]
            else:
                raise ValueError(f"Environment variable not found: {var_name}")
        
        return re.sub(r'\$\{([^}]+)\}', replace_var, value)
    
    def _process_params(self, params: Dict[str, Any], env_vars: Dict[str, str]) -> Dict[str, Any]:
        """Process server parameters, substituting environment variables."""
        processed = {}
        
        for key, value in params.items():
            if isinstance(value, str):
                processed[key] = self._substitute_env_vars(value, env_vars)
            elif isinstance(value, list):
                processed[key] = [
                    self._substitute_env_vars(item, env_vars) if isinstance(item, str) else item
                    for item in value
                ]
            elif isinstance(value, dict):
                processed[key] = self._process_params(value, env_vars)
            else:
                processed[key] = value
                
        return processed
    
    def _resolve_path(self, path: str) -> str:
        """Resolve relative paths relative to the base directory."""
        if os.path.isabs(path):
            return path
        return os.path.join(self.base_dir, path)
    
    def get_enabled_servers(self) -> List[Dict[str, Any]]:
        """Get list of enabled server configurations."""
        servers = self.config.get('mcp_servers', [])
        return [server for server in servers if server.get('enabled', True)]
    
    def create_mcp_servers(self) -> List[MCPServer]:
        """
        Create MCP server instances from the configuration.
        
        Returns:
            List of configured MCP server instances
        """
        servers = []
        enabled_configs = self.get_enabled_servers()
        default_env = self.config.get('default_env', {})
        
        # Resolve relative paths in default_env
        resolved_env = {}
        for key, value in default_env.items():
            if isinstance(value, str) and not os.path.isabs(value):
                resolved_env[key] = self._resolve_path(value)
            else:
                resolved_env[key] = value
        
        for server_config in enabled_configs:
            try:
                server = self._create_server(server_config, resolved_env)
                if server:
                    servers.append(server)
            except Exception as e:
                print(f"Warning: Failed to create server '{server_config.get('name', 'Unknown')}': {e}")
                
        return servers
    
    def _create_server(self, config: Dict[str, Any], env_vars: Dict[str, str]) -> Optional[MCPServer]:
        """Create a single MCP server instance from configuration."""
        server_type = config.get('type', 'stdio')
        name = config.get('name', 'Unnamed Server')
        params = config.get('params', {})
        
        # Process parameters with environment variable substitution
        processed_params = self._process_params(params, env_vars)
        
        if server_type == 'stdio':
            return MCPServerStdio(name=name, params=processed_params)
        else:
            raise ValueError(f"Unsupported server type: {server_type}")


def load_mcp_servers_from_config(config_path: str, base_dir: Optional[str] = None) -> List[MCPServer]:
    """
    Convenience function to load MCP servers from a JSON configuration file.
    
    Args:
        config_path: Path to the JSON configuration file
        base_dir: Base directory for resolving relative paths
        
    Returns:
        List of configured MCP server instances
    """
    loader = MCPConfigLoader(config_path, base_dir)
    return loader.create_mcp_servers()
