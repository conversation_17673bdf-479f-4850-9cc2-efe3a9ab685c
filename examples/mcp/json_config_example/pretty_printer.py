"""
Pretty Printer for Chat Responses

This module provides beautiful formatting for chat responses similar to Claude <PERSON>
and mcph<PERSON>, with syntax highlighting, structured output, and rich formatting.
"""

import re
import json
from typing import Any, Dict, List, Optional
from datetime import datetime

try:
    from rich.console import Console
    from rich.markdown import Markdown
    from rich.syntax import Syntax
    from rich.panel import Panel
    from rich.text import Text
    from rich.table import Table
    from rich.columns import Columns
    from rich.rule import Rule
    from rich import box
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

try:
    from pygments import highlight
    from pygments.lexers import get_lexer_by_name, guess_lexer
    from pygments.formatters import TerminalFormatter
    PYGMENTS_AVAILABLE = True
except ImportError:
    PYGMENTS_AVAILABLE = False


class PrettyPrinter:
    """Enhanced pretty printer for chat responses with rich formatting."""

    def __init__(self, use_rich: bool = True):
        """Initialize the pretty printer."""
        self.use_rich = use_rich and RICH_AVAILABLE
        if self.use_rich:
            self.console = Console()

    def print_welcome(self, servers: List[str], session_start: datetime):
        """Print a beautiful welcome message."""
        if self.use_rich:
            self._print_rich_welcome(servers, session_start)
        else:
            self._print_simple_welcome(servers, session_start)

    def _print_rich_welcome(self, servers: List[str], session_start: datetime):
        """Print welcome message using Rich."""
        # Header
        self.console.print(Rule("🚀 Interactive Chat Client with MCP Server Support", style="bold blue"))

        # Session info
        session_info = Table.grid(padding=1)
        session_info.add_column(style="cyan", justify="right")
        session_info.add_column(style="white")
        session_info.add_row("📅 Session started:", session_start.strftime('%Y-%m-%d %H:%M:%S'))
        session_info.add_row("🔧 MCP servers:", f"{len(servers)} configured")

        # Server list
        if servers:
            server_list = "\n".join(f"   • {server}" for server in servers)
            session_info.add_row("", server_list)

        self.console.print(Panel(session_info, title="Session Info", border_style="blue"))

        # Tips
        tips = """💡 **Tips:**
• Ask me to list available tools to see what I can do
• I can read files, manage resources, search data, and more
• Be specific about what you want to accomplish

🎮 **Commands:**
• `help` - Show available commands
• `tools` - List available tools
• `history` - Show conversation history
• `clear` - Clear the screen
• `save` - Save conversation to file
• `/q` or `quit` - End the session (also Ctrl+C)"""

        self.console.print(Panel(Markdown(tips), title="Getting Started", border_style="green"))
        self.console.print()

    def _print_simple_welcome(self, servers: List[str], session_start: datetime):
        """Print welcome message using simple formatting."""
        print("=" * 70)
        print("🚀 Interactive Chat Client with MCP Server Support")
        print("=" * 70)
        print(f"📅 Session started: {session_start.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔧 MCP servers: {len(servers)} configured")
        for server in servers:
            print(f"   • {server}")
        print()
        print("💡 Tips:")
        print("   • Ask me to list available tools to see what I can do")
        print("   • Be specific about what you want to accomplish")
        print()
        print("🎮 Commands: help, tools, history, clear, save, /q (quit)")
        print("   • Use /q to quit quickly, or press Ctrl+C anytime")
        print("-" * 70)

    def print_user_input(self, text: str):
        """Print user input with nice formatting."""
        if self.use_rich:
            self.console.print(f"💬 [bold cyan]You:[/bold cyan] {text}")
        else:
            print(f"💬 You: {text}")

    def print_assistant_response(self, response: str, thinking: bool = False):
        """Print assistant response with beautiful formatting."""
        if self.use_rich:
            self._print_rich_response(response, thinking)
        else:
            self._print_simple_response(response, thinking)

    def _print_rich_response(self, response: str, thinking: bool = False):
        """Print response using Rich with syntax highlighting and formatting."""
        # Detect and format different types of content
        formatted_response = self._format_response_content(response)

        if thinking:
            self.console.print("🤔 [italic yellow]Assistant is thinking...[/italic yellow]")

        # Print with assistant label
        self.console.print("🤖 [bold green]Assistant:[/bold green]")

        # Check if response contains code blocks
        if "```" in response:
            self._print_with_code_blocks(response)
        else:
            # Try to render as markdown if it looks like markdown
            if self._looks_like_markdown(response):
                try:
                    self.console.print(Markdown(response))
                except:
                    self.console.print(response)
            else:
                self.console.print(response)

        self.console.print()

    def _print_simple_response(self, response: str, thinking: bool = False):
        """Print response using simple formatting with basic syntax highlighting."""
        if thinking:
            print("🤔 Assistant is thinking...")

        print("🤖 Assistant:")

        # Basic code block detection and highlighting
        if "```" in response:
            self._print_simple_code_blocks(response)
        else:
            print(response)
        print()

    def _format_response_content(self, response: str) -> str:
        """Format response content with better structure."""
        # Add bullet points for lists
        lines = response.split('\n')
        formatted_lines = []

        for line in lines:
            stripped = line.strip()
            # Convert various list formats to bullet points
            if stripped.startswith('- '):
                line = line.replace('- ', '• ')
            elif stripped.startswith('* '):
                line = line.replace('* ', '• ')
            elif re.match(r'^\d+\.\s', stripped):
                # Keep numbered lists as is
                pass
            formatted_lines.append(line)

        return '\n'.join(formatted_lines)

    def _looks_like_markdown(self, text: str) -> bool:
        """Check if text looks like markdown."""
        markdown_indicators = [
            '**', '*', '`', '#', '- ', '* ', '[', ']', '|'
        ]
        return any(indicator in text for indicator in markdown_indicators)

    def _print_with_code_blocks(self, response: str):
        """Print response with syntax-highlighted code blocks."""
        parts = response.split('```')

        for i, part in enumerate(parts):
            if i % 2 == 0:
                # Regular text
                if part.strip():
                    if self._looks_like_markdown(part):
                        try:
                            self.console.print(Markdown(part))
                        except:
                            self.console.print(part)
                    else:
                        self.console.print(part)
            else:
                # Code block
                lines = part.split('\n')
                language = lines[0].strip() if lines else ''
                code = '\n'.join(lines[1:]) if len(lines) > 1 else part

                if language and code.strip():
                    try:
                        syntax = Syntax(code, language, theme="monokai", line_numbers=True)
                        self.console.print(Panel(syntax, title=f"Code ({language})", border_style="yellow"))
                    except:
                        # Fallback to plain code block
                        self.console.print(Panel(code, title="Code", border_style="yellow"))
                else:
                    # No language specified
                    self.console.print(Panel(part, title="Code", border_style="yellow"))

    def _print_simple_code_blocks(self, response: str):
        """Print code blocks with simple formatting."""
        parts = response.split('```')

        for i, part in enumerate(parts):
            if i % 2 == 0:
                # Regular text
                if part.strip():
                    print(part)
            else:
                # Code block
                lines = part.split('\n')
                language = lines[0].strip() if lines else ''
                code = '\n'.join(lines[1:]) if len(lines) > 1 else part

                print(f"\n--- Code ({language if language else 'text'}) ---")

                # Try pygments highlighting if available
                if PYGMENTS_AVAILABLE and language:
                    try:
                        lexer = get_lexer_by_name(language)
                        highlighted = highlight(code, lexer, TerminalFormatter())
                        print(highlighted)
                    except:
                        print(code)
                else:
                    print(code)
                print("--- End Code ---\n")

    def print_error(self, error: str):
        """Print error message with formatting."""
        if self.use_rich:
            self.console.print(f"❌ [bold red]Error:[/bold red] {error}")
        else:
            print(f"❌ Error: {error}")

    def print_info(self, message: str):
        """Print info message with formatting."""
        if self.use_rich:
            self.console.print(f"ℹ️  [blue]{message}[/blue]")
        else:
            print(f"ℹ️  {message}")

    def print_success(self, message: str):
        """Print success message with formatting."""
        if self.use_rich:
            self.console.print(f"✅ [green]{message}[/green]")
        else:
            print(f"✅ {message}")

    def print_warning(self, message: str):
        """Print warning message with formatting."""
        if self.use_rich:
            self.console.print(f"⚠️  [yellow]{message}[/yellow]")
        else:
            print(f"⚠️  {message}")

    def print_thinking(self):
        """Print thinking indicator."""
        if self.use_rich:
            self.console.print("🤔 [italic yellow]Assistant is thinking...[/italic yellow]")
        else:
            print("🤔 Assistant is thinking...")

    def print_tool_call(self, tool_name: str, server_name: str = "", args: Dict[str, Any] = None):
        """Print tool call indicator."""
        if self.use_rich:
            if server_name:
                self.console.print(f"🔧 [cyan]Calling tool:[/cyan] [bold]{tool_name}[/bold] [dim]({server_name})[/dim]")
            else:
                self.console.print(f"🔧 [cyan]Calling tool:[/cyan] [bold]{tool_name}[/bold]")

            if args and len(args) > 0:
                # Show tool arguments in a compact format
                args_str = ", ".join([f"{k}={str(v)[:50]}{'...' if len(str(v)) > 50 else ''}" for k, v in args.items()])
                self.console.print(f"   [dim]Arguments: {args_str}[/dim]")
        else:
            if server_name:
                print(f"🔧 Calling tool: {tool_name} ({server_name})")
            else:
                print(f"🔧 Calling tool: {tool_name}")

            if args and len(args) > 0:
                args_str = ", ".join([f"{k}={str(v)[:50]}{'...' if len(str(v)) > 50 else ''}" for k, v in args.items()])
                print(f"   Arguments: {args_str}")

    def print_tool_result(self, tool_name: str, success: bool = True, result_summary: str = ""):
        """Print tool result indicator."""
        if self.use_rich:
            if success:
                status_icon = "✅"
                status_color = "green"
            else:
                status_icon = "❌"
                status_color = "red"

            self.console.print(f"{status_icon} [dim]Tool [bold]{tool_name}[/bold] {'completed' if success else 'failed'}[/dim]")

            if result_summary:
                # Truncate long results
                if len(result_summary) > 100:
                    result_summary = result_summary[:100] + "..."
                self.console.print(f"   [dim italic]{result_summary}[/dim italic]")
        else:
            status_icon = "✅" if success else "❌"
            print(f"{status_icon} Tool {tool_name} {'completed' if success else 'failed'}")

            if result_summary:
                if len(result_summary) > 100:
                    result_summary = result_summary[:100] + "..."
                print(f"   {result_summary}")

    def print_multiple_tools(self, tools: List[str], server_name: str = ""):
        """Print indicator for multiple tool calls."""
        if self.use_rich:
            tools_str = ", ".join(tools)
            if server_name:
                self.console.print(f"🔧 [cyan]Calling {len(tools)} tools:[/cyan] [bold]{tools_str}[/bold] [dim]({server_name})[/dim]")
            else:
                self.console.print(f"🔧 [cyan]Calling {len(tools)} tools:[/cyan] [bold]{tools_str}[/bold]")
        else:
            tools_str = ", ".join(tools)
            if server_name:
                print(f"🔧 Calling {len(tools)} tools: {tools_str} ({server_name})")
            else:
                print(f"🔧 Calling {len(tools)} tools: {tools_str}")

    def print_rule(self, title: str = ""):
        """Print a separator rule."""
        if self.use_rich:
            self.console.print(Rule(title, style="dim"))
        else:
            print("-" * 50)
            if title:
                print(f" {title} ")
                print("-" * 50)

    def print_table(self, data: List[Dict[str, Any]], title: str = ""):
        """Print data in a table format."""
        if not data:
            return

        if self.use_rich:
            table = Table(title=title, box=box.ROUNDED)

            # Add columns
            if data:
                for key in data[0].keys():
                    table.add_column(key.replace('_', ' ').title(), style="cyan")

                # Add rows
                for row in data:
                    table.add_row(*[str(value) for value in row.values()])

            self.console.print(table)
        else:
            if title:
                print(f"\n{title}")
                print("-" * len(title))

            if data:
                # Simple table format
                headers = list(data[0].keys())
                print(" | ".join(headers))
                print("-" * (len(" | ".join(headers))))

                for row in data:
                    print(" | ".join(str(row.get(header, "")) for header in headers))
            print()

    def clear_screen(self):
        """Clear the screen."""
        if self.use_rich:
            self.console.clear()
        else:
            import os
            os.system('clear' if os.name == 'posix' else 'cls')
