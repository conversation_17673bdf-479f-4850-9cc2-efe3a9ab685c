#!/usr/bin/env python3
"""
Test script to verify quit commands work correctly.

This script tests the /q and Ctrl+C functionality in the chat clients.
"""

import async<PERSON>
from pretty_chat_client import PrettyChatClient
from enhanced_chat_client import <PERSON>hancedChatClient
from chat_client import ChatClient


class MockMCPServer:
    """Mock MCP server for testing."""
    
    def __init__(self, name: str):
        self.name = name
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


async def test_quit_commands():
    """Test quit command handling in all chat clients."""
    
    print("🧪 Testing Quit Commands")
    print("=" * 30)
    
    # Create mock server
    mock_server = MockMCPServer("Test Server")
    
    # Test cases
    quit_commands = ['/q', 'q', 'quit', 'exit', 'bye']
    
    for client_class, client_name in [
        (PrettyChatClient, "Pretty Chat Client"),
        (EnhancedChatClient, "Enhanced Chat Client"),
        (ChatClient, "Basic Chat Client"),
    ]:
        print(f"\n🔧 Testing {client_name}")
        print("-" * 40)
        
        client = client_class([mock_server])
        
        for command in quit_commands:
            try:
                result = await client.handle_special_commands(command)
                if result:
                    print(f"✅ '{command}' -> Quit command recognized")
                else:
                    print(f"❌ '{command}' -> Not recognized as quit command")
            except Exception as e:
                print(f"❌ '{command}' -> Error: {e}")
    
    print("\n🎉 Quit command testing completed!")
    print("\nAll chat clients should now support:")
    print("• /q - Quick quit")
    print("• q - Simple quit")
    print("• quit - Standard quit")
    print("• exit - Alternative quit")
    print("• bye - Friendly quit")
    print("• Ctrl+C - Force quit (handled in main loop)")


def main():
    """Main test function."""
    
    print("This script tests the quit command functionality.")
    print("It verifies that /q and other quit commands work correctly.")
    print()
    
    try:
        asyncio.run(test_quit_commands())
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted with Ctrl+C - this is expected behavior!")
        print("Ctrl+C handling works correctly!")


if __name__ == "__main__":
    main()
