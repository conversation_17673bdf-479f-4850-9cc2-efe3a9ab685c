"""
    pygments.lexers._ada_builtins
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    Ada builtins.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

KEYWORD_LIST = (
    'abort',
    'abs',
    'abstract',
    'accept',
    'access',
    'aliased',
    'all',
    'array',
    'at',
    'begin',
    'body',
    'case',
    'constant',
    'declare',
    'delay',
    'delta',
    'digits',
    'do',
    'else',
    'elsif',
    'end',
    'entry',
    'exception',
    'exit',
    'interface',
    'for',
    'goto',
    'if',
    'is',
    'limited',
    'loop',
    'new',
    'null',
    'of',
    'or',
    'others',
    'out',
    'overriding',
    'pragma',
    'protected',
    'raise',
    'range',
    'record',
    'renames',
    'requeue',
    'return',
    'reverse',
    'select',
    'separate',
    'some',
    'subtype',
    'synchronized',
    'task',
    'tagged',
    'terminate',
    'then',
    'type',
    'until',
    'when',
    'while',
    'xor'
)

BUILTIN_LIST = (
    'Address',
    'Byte',
    'Boolean',
    'Character',
    'Controlled',
    'Count',
    'Cursor',
    'Duration',
    'File_Mode',
    'File_Type',
    'Float',
    'Generator',
    'Integer',
    'Long_Float',
    'Long_Integer',
    'Long_Long_Float',
    'Long_Long_Integer',
    'Natural',
    'Positive',
    'Reference_Type',
    'Short_Float',
    'Short_Integer',
    'Short_Short_Float',
    'Short_Short_Integer',
    'String',
    'Wide_Character',
    'Wide_String'
)
