import asyncio
import os
import shutil
import argparse
from typing import List

from agents import Agent, OpenAIChatCompletionsM<PERSON>l, Runner, set_tracing_disabled
from agents.mcp import MCPServer
from dotenv import load_dotenv
from openai import AsyncAzureOpenAI

from mcp_loader import load_mcp_servers_from_config


def get_azure_open_ai_client():
    """
    Creates and returns Azure OpenAI client instance.
    
    Returns:
        AsyncAzureOpenAI: Configured Azure OpenAI client
    """
    load_dotenv()
    
    return AsyncAzureOpenAI(
        api_key=os.getenv("AZURE_OPENAI_API_KEY"),
        api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
        azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
    )


async def run_with_mcp_servers(mcp_servers: List[MCPServer]):
    """Run the agent with the provided MCP servers."""
    
    azure_open_ai_client = get_azure_open_ai_client()
    set_tracing_disabled(disabled=True)

    # Create agent with all MCP servers
    agent = Agent(
        name="Assistant",
        instructions="Use the available tools to help answer questions. You have access to multiple MCP servers that provide different capabilities.",
        model=OpenAIChatCompletionsModel(
            model=os.getenv("AZURE_OPENAI_CHAT_DEPLOYMENT_NAME"), 
            openai_client=azure_open_ai_client
        ),
        mcp_servers=mcp_servers,
    )

    print(f"Agent initialized with {len(mcp_servers)} MCP servers:")
    for server in mcp_servers:
        print(f"  - {server.name}")
    print()

    # Test the available capabilities
    test_messages = [
        "What tools do you have available?",
        "Read the files in the sample_files folder and list them.",
        "What is my #1 favorite book?",
        "Look at my favorite songs and suggest one new song that I might like."
    ]

    for message in test_messages:
        print(f"Running: {message}")
        try:
            result = await Runner.run(starting_agent=agent, input=message)
            print(f"Response: {result.final_output}")
        except Exception as e:
            print(f"Error: {e}")
        print("-" * 50)


async def main():
    """Main function that loads MCP servers from config and runs the agent."""
    
    parser = argparse.ArgumentParser(description="Run Azure AI Foundry Agent with MCP servers from JSON config")
    parser.add_argument(
        "--config", 
        default="mcp_config.json",
        help="Path to MCP configuration JSON file (default: mcp_config.json)"
    )
    
    args = parser.parse_args()
    
    # Check if npx is available (required for most MCP servers)
    if not shutil.which("npx"):
        print("Warning: npx is not installed. Some MCP servers may not work.")
        print("Install it with: npm install -g npx")
    
    # Get the directory of this script for resolving relative paths
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = args.config
    
    # If config path is relative, resolve it relative to the script directory
    if not os.path.isabs(config_path):
        config_path = os.path.join(current_dir, config_path)
    
    try:
        print(f"Loading MCP servers from: {config_path}")
        mcp_servers = load_mcp_servers_from_config(config_path, current_dir)
        
        if not mcp_servers:
            print("No enabled MCP servers found in configuration.")
            return
            
        print(f"Successfully loaded {len(mcp_servers)} MCP server(s)")
        
        # Run the agent with all loaded servers
        async with asyncio.TaskGroup() as tg:
            # Start all servers
            for server in mcp_servers:
                tg.create_task(server.__aenter__())
        
        try:
            await run_with_mcp_servers(mcp_servers)
        finally:
            # Clean up servers
            for server in mcp_servers:
                try:
                    await server.__aexit__(None, None, None)
                except Exception as e:
                    print(f"Error closing server {server.name}: {e}")
                    
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    asyncio.run(main())
