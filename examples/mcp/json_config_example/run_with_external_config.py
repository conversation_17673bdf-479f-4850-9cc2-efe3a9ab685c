#!/usr/bin/env python3
"""
Utility script to run the MCP example with an external configuration file.

This script is specifically designed to work with the MCP configuration format
used in /Users/<USER>/dev/personal/local_llm/docker_compose_ollama/mcp.json

Usage:
    python run_with_external_config.py /path/to/your/mcp.json
"""

import asyncio
import sys
import os
from main_azure_ai_foundry import main as run_main


async def main():
    """Main function that sets up the config path and runs the main application."""
    
    if len(sys.argv) != 2:
        print("Usage: python run_with_external_config.py <path_to_mcp_config.json>")
        print("Example: python run_with_external_config.py /Users/<USER>/dev/personal/local_llm/docker_compose_ollama/mcp.json")
        return 1
    
    config_path = sys.argv[1]
    
    if not os.path.exists(config_path):
        print(f"Error: Configuration file not found: {config_path}")
        return 1
    
    # Override sys.argv to pass the config path to the main script
    original_argv = sys.argv
    sys.argv = ["main_azure_ai_foundry.py", "--config", config_path]
    
    try:
        result = await run_main()
        return result or 0
    finally:
        # Restore original argv
        sys.argv = original_argv


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
