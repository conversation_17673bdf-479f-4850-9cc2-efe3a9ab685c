{"mcp_servers": [{"name": "Filesystem Server", "type": "stdio", "enabled": true, "params": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "${SAMPLE_FILES_DIR}"], "env": {}}}, {"name": "SQLite Server", "type": "stdio", "enabled": false, "params": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite", "${DATABASE_PATH}"], "env": {}}}, {"name": "Web Search Server", "type": "stdio", "enabled": false, "params": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "${BRAVE_API_KEY}"}}}, {"name": "GitHub Server", "type": "stdio", "enabled": false, "params": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}"}}}], "default_env": {"SAMPLE_FILES_DIR": "./sample_files", "DATABASE_PATH": "./data.db"}}