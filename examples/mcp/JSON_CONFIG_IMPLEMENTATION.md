# MCP JSON Configuration Implementation

This document describes the implementation of JSON configuration support for MCP servers in the Azure AI Foundry Agents Samples project.

## Overview

The implementation adds support for loading MCP server configurations from JSON files, making it easier to manage multiple MCP servers and their configurations. This extends the existing filesystem MCP example to support a more flexible configuration approach.

## Implementation Structure

### Core Components

1. **`json_config_example/mcp_loader.py`** - Core utility for loading MCP configurations
2. **`json_config_example/main_azure_ai_foundry.py`** - Main application using JSON config
3. **`json_config_example/mcp_config.json`** - Example configuration file
4. **`json_config_example/test_config_loader.py`** - Testing utility
5. **`json_config_example/run_with_external_config.py`** - Utility for external configs

### JSON Configuration Format

The implementation supports the JSON format you specified:

```json
{
  "mcpServers": {
    "ServerName": {
      "command": "command_to_run",
      "args": ["arg1", "arg2"],
      "env": {
        "ENV_VAR": "value"
      }
    }
  }
}
```

### Key Features

1. **Flexible Server Configuration**: Supports any MCP server that can be run via stdio
2. **Path Resolution**: Automatically resolves relative paths relative to the config file
3. **Environment Variables**: Supports environment variable configuration per server
4. **Command Line Support**: Can specify custom configuration files via CLI arguments
5. **Error Handling**: Graceful handling of configuration errors with informative messages

## Usage Examples

### Using Your Existing Configuration

To use your existing MCP configuration file:

```bash
cd examples/mcp/json_config_example
python main_azure_ai_foundry.py --config /Users/<USER>/dev/personal/local_llm/docker_compose_ollama/mcp.json
```

Or using the utility script:

```bash
python run_with_external_config.py /Users/<USER>/dev/personal/local_llm/docker_compose_ollama/mcp.json
```

### Testing Configuration

To test configuration loading without running servers:

```bash
python test_config_loader.py /path/to/your/mcp.json
```

### Using Default Configuration

```bash
python main_azure_ai_foundry.py
```

This uses the included `mcp_config.json` with filesystem and control plane servers.

## Integration with Existing Code

### Enhanced Filesystem Example

The `filesystem_example/main_with_json_support.py` shows how to add JSON config support to existing MCP examples:

```python
# Can run with default filesystem server
python main_with_json_support.py

# Or with JSON configuration
python main_with_json_support.py --config /path/to/config.json
```

## Configuration Examples

### Your Control Plane Server

```json
{
  "mcpServers": {
    "ControlPlaneServer": {
      "command": "uv",
      "args": [
        "run",
        "--directory",
        "/Users/<USER>/dev/work/repos/control-plane-mcp-server",
        "main.py"
      ],
      "env": {
        "FACETS_PROFILE": "facetsdemo"
      }
    }
  }
}
```

### Multiple Servers

```json
{
  "mcpServers": {
    "FilesystemServer": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "./sample_files"],
      "env": {}
    },
    "ControlPlaneServer": {
      "command": "uv",
      "args": ["run", "--directory", "/path/to/control-plane-server", "main.py"],
      "env": {
        "FACETS_PROFILE": "facetsdemo"
      }
    }
  }
}
```

## Technical Details

### MCPConfigLoader Class

The `MCPConfigLoader` class provides:

- **Configuration Loading**: Loads and validates JSON configuration files
- **Server Enumeration**: Lists all configured servers
- **Server Creation**: Creates `MCPServerStdio` instances from configuration
- **Path Resolution**: Resolves relative paths relative to config file location

### Error Handling

The implementation includes comprehensive error handling:

- Invalid JSON format detection
- Missing configuration files
- Invalid server configurations
- Missing required commands or arguments

### Async Context Management

The main application properly handles async context managers for MCP servers:

- Single server: Direct async context manager
- Two servers: Nested async context managers
- Multiple servers: Sequential handling (limitation noted)

## Benefits

1. **Centralized Configuration**: All MCP server configurations in one place
2. **Easy Management**: Add, remove, or modify servers without code changes
3. **Reusability**: Configuration files can be shared across different applications
4. **Flexibility**: Supports any stdio-based MCP server
5. **Path Independence**: Relative paths resolved automatically

## Future Enhancements

Potential improvements for the implementation:

1. **Environment Variable Substitution**: Support for `${VAR}` syntax in configurations
2. **Server Filtering**: Enable/disable servers without removing from config
3. **Validation**: JSON schema validation for configuration files
4. **Better Async Handling**: Improved support for many concurrent servers
5. **Configuration Templates**: Predefined templates for common server types

## Testing

The implementation includes testing utilities:

- **`test_config_loader.py`**: Validates configuration loading without running servers
- **Sample configurations**: Multiple example configurations for testing
- **Error scenarios**: Tests for various error conditions

This implementation provides a solid foundation for managing MCP servers via JSON configuration while maintaining compatibility with the existing codebase.
