import asyncio
import os
import shutil
import argparse

from agents import Agent, OpenAIChatCompletionsM<PERSON><PERSON>, Runner, set_tracing_disabled
from agents.mcp import MCPServer, MCPServerStdio
from dotenv import load_dotenv
from openai import AsyncAzureOpenAI


def get_azure_open_ai_client():
    """
    Creates and returns Azure OpenAI client instance.
    
    Returns:
        AsyncAzureOpenAI: Configured Azure OpenAI client
    """
    load_dotenv()
    
    return AsyncAzureOpenAI(
        api_key=os.getenv("AZURE_OPENAI_API_KEY"),
        api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
        azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
    )


async def run(mcp_server: MCPServer):

    azure_open_ai_client = get_azure_open_ai_client()
    set_tracing_disabled(disabled=True)

    agent = Agent(
        name="Assistant",
        instructions="Use the tools to read the filesystem and answer questions based on those files.",
        model=OpenAIChatCompletionsModel(model=os.getenv("AZURE_OPENAI_CHAT_DEPLOYMENT_NAME"), 
                                         openai_client=azure_open_ai_client),
        mcp_servers=[mcp_server],
    )

    # List the files it can read
    message = "Read the files in `sample_files` folder, and list them."
    print(f"Running: {message}")
    result = await Runner.run(starting_agent=agent, input=message)
    print(result.final_output)

    # Ask about books
    message = "What is my #1 favorite book?"
    print(f"\n\nRunning: {message}")
    result = await Runner.run(starting_agent=agent, input=message)
    print(result.final_output)

    # Ask a question that reads then reasons.
    message = "Look at my favorite songs. Suggest one new song that I might like."
    print(f"\n\nRunning: {message}")
    result = await Runner.run(starting_agent=agent, input=message)
    print(result.final_output)


async def main():
    parser = argparse.ArgumentParser(description="Run filesystem MCP example with optional JSON config support")
    parser.add_argument(
        "--config", 
        help="Path to MCP configuration JSON file (if not provided, uses default filesystem server)"
    )
    
    args = parser.parse_args()
    
    # Check if npx is available
    if not shutil.which("npx"):
        raise RuntimeError("npx is not installed. Please install it with `npm install -g npx`.")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    samples_dir = os.path.join(current_dir, "sample_files")
    
    if args.config:
        # Use JSON configuration
        print(f"Loading MCP servers from: {args.config}")
        
        # Import the JSON loader from the json_config_example
        import sys
        json_example_dir = os.path.join(os.path.dirname(current_dir), "json_config_example")
        sys.path.insert(0, json_example_dir)
        
        from mcp_loader import load_mcp_servers_from_config
        
        try:
            mcp_servers = load_mcp_servers_from_config(args.config, current_dir)
            
            if not mcp_servers:
                print("No enabled MCP servers found in configuration.")
                return
            
            print(f"Successfully loaded {len(mcp_servers)} MCP server(s)")
            
            # For simplicity, just use the first server
            if len(mcp_servers) > 1:
                print(f"Note: Using first server ({mcp_servers[0].name}) from configuration")
            
            async with mcp_servers[0] as server:
                await run(server)
                
        except Exception as e:
            print(f"Error loading from JSON config: {e}")
            return 1
    else:
        # Use default filesystem server
        print("Using default filesystem server")
        async with MCPServerStdio(
            name="Filesystem Server, via npx",
            params={
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-filesystem", samples_dir],
            },
        ) as server:
            await run(server)


if __name__ == "__main__":
    asyncio.run(main())
